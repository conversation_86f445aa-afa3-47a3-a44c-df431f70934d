# ESLint Standardization Plan

## Current Issues
- Version conflicts between ESLint 8.57.1 and 9.29.0
- TypeScript ESLint version mismatches (7.2.0 vs 8.34.1)
- Incompatible rule configurations causing build failures
- React projects need specific configurations

## Recommended Solution

### 1. Standardize Versions (Root package.json)
```json
{
  "devDependencies": {
    "eslint": "^9.18.0",
    "@typescript-eslint/eslint-plugin": "^8.32.0",
    "@typescript-eslint/parser": "^8.32.0",
    "typescript-eslint": "^8.20.0"
  }
}
```

### 2. Project-Specific Configs

#### **NestJS Projects** (use root config)
- Remove individual eslint configs
- Use workspace root `eslint.config.mjs`
- Update package.json lint scripts to use root config

#### **React Projects** (keep specific configs)
- Keep `apps/webchat.test-client/eslint.config.mjs`
- Add similar config for `apps/copilotkit.test-client`
- Use compatible versions with React ecosystem

### 3. Fix Version Conflicts

#### **Update webchat.test-client package.json:**
```json
{
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^8.32.0",
    "@typescript-eslint/parser": "^8.32.0",
    "eslint": "^9.18.0"
  }
}
```

#### **Update copilotkit.test-client similarly**

### 4. Resolve Rule Conflicts

#### **Root eslint.config.mjs** - Add compatibility rules:
```javascript
{
  rules: {
    // Fix no-unused-expressions conflict
    '@typescript-eslint/no-unused-expressions': [
      'warn',
      {
        allowShortCircuit: true,
        allowTernary: true,
        allowTaggedTemplates: true
      }
    ]
  }
}
```

### 5. Lint Script Standardization

#### **Root package.json:**
```json
{
  "scripts": {
    "lint": "turbo lint",
    "lint:fix": "turbo lint -- --fix"
  }
}
```

#### **Individual projects:**
```json
{
  "scripts": {
    "lint": "eslint \"{src,apps,libs,test}/**/*.{ts,tsx}\" --max-warnings 0",
    "lint:fix": "eslint \"{src,apps,libs,test}/**/*.{ts,tsx}\" --fix"
  }
}
```

## Benefits
- Consistent linting across all projects
- No more version conflicts
- Faster CI/CD builds
- Better developer experience
