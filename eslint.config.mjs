import js from '@eslint/js';
import prettier from 'eslint-config-prettier';
import globals from 'globals';
import tseslint from 'typescript-eslint';

export default tseslint.config(
  js.configs.recommended,
  ...tseslint.configs.recommendedTypeChecked,
  prettier,
  {
    languageOptions: {
      parserOptions: {
        project: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },
  {
    ignores: [
      // Dependencies
      'node_modules/**',
      '**/node_modules/**',

      // Build outputs
      'dist/**',
      '**/dist/**',
      'build/**',
      '**/build/**',

      // Lock files
      'pnpm-lock.yaml',
      'package-lock.json',
      'yarn.lock',

      // Environment files
      '.env',
      '.env.*',

      // Logs
      '*.log',
      'logs/**',
      '**/logs/**',

      // Coverage reports
      'coverage/**',
      '**/coverage/**',
      '.nyc_output/**',

      // TypeScript build info
      '*.tsbuildinfo',

      // Generated code - Twenty SDK (Fern generated)
      'libs/twenty-sdk/src/**',
      'twenty-sdk-generator/**',

      // Public assets
      '**/public/**',

      // Configuration files
      '*.config.js',
      '*.config.mjs',

      // Docker
      'Dockerfile*',
      'docker-compose*.yml',

      // CI/CD
      'bitbucket-pipelines.yml',
      '.github/**',
      '.gitlab-ci.yml',
    ],
  },
  {
    rules: {
      // Relaxed rules for development
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-unsafe-assignment': 'warn',
      '@typescript-eslint/no-unsafe-member-access': 'warn',
      '@typescript-eslint/no-unsafe-call': 'warn',
      '@typescript-eslint/no-unsafe-argument': 'warn',
      '@typescript-eslint/no-unsafe-return': 'warn',
      '@typescript-eslint/no-floating-promises': 'warn',
      '@typescript-eslint/require-await': 'warn',
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-non-null-asserted-optional-chain': 'warn',
      '@typescript-eslint/no-redundant-type-constituents': 'warn',
      '@typescript-eslint/no-misused-promises': 'warn',
      '@typescript-eslint/no-base-to-string': 'warn',
      // Fix no-unused-expressions conflict
      '@typescript-eslint/no-unused-expressions': [
        'warn',
        {
          allowShortCircuit: true,
          allowTernary: true,
          allowTaggedTemplates: true,
        },
      ],
      'no-console': 'warn',
      eqeqeq: 'warn',
    },
  },
  // Node.js scripts configuration
  {
    files: ['scripts/**/*.js', 'scripts/**/*.mjs'],
    languageOptions: {
      globals: {
        ...globals.node,
      },
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: 'script',
      },
    },
    rules: {
      // Allow CommonJS require in Node.js scripts
      '@typescript-eslint/no-require-imports': 'off',
      'no-undef': 'off', // Node.js globals are handled by globals.node
      'no-console': 'off', // Allow console in scripts
      // Disable TypeScript-specific rules for plain JS scripts
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
    },
  },
);
