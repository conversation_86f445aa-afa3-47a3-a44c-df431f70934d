{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "Node", "declaration": true, "declarationMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "noEmit": false, "incremental": true, "resolveJsonModule": true, "verbatimModuleSyntax": false, "baseUrl": ".", "paths": {"@lisa/common": ["./libs/common/src"], "@lisa/common/*": ["./libs/common/src/*"], "@lisa/crm-sdk": ["./libs/crm-sdk/src"], "@lisa/crm-sdk/*": ["./libs/crm-sdk/src/*"], "@lisa/lead-workflow": ["./libs/lead-workflow/src"], "@lisa/lead-workflow/*": ["./libs/lead-workflow/src/*"], "@lisa/main-workflow": ["./libs/main-workflow/src"], "@lisa/main-workflow/*": ["./libs/main-workflow/src/*"], "@lisa/twenty-sdk": ["./libs/twenty-sdk/src"], "@lisa/twenty-sdk/*": ["./libs/twenty-sdk/src/*"], "@lisa/twenty-sdk/cjs": ["./libs/twenty-sdk/src/cjs"], "@lisa/twenty-sdk/cjs/*": ["./libs/twenty-sdk/src/cjs/*"], "@lisa/twenty-sdk/cjs/api": ["./libs/twenty-sdk/src/cjs/api"]}}}